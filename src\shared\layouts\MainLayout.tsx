import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Icon } from '@/shared/components/common';
import ChatButton from '@/shared/components/layout/chat-button/ChatButton';
import ChatPanel from '@/shared/components/layout/chat-panel/ChatPanel';
import { userMenuItems } from '@/shared/components/layout/chat-panel/menu-items';
import ResizableChatLayout from '@/shared/components/layout/ResizableChatLayout';
import SidebarMenu from '@/shared/components/layout/sidebar-menu/SidebarMenu';
import StableViewPanel from '@/shared/components/layout/StableViewPanel';
import { useChatPanel } from '@/shared/contexts';
import { useIsMobile } from '@/shared/hooks/common';
import { clearChatNotifications } from '@/shared/store/slices/chatSlice';

interface MainLayoutProps {
  children: ReactNode;
  title: string;
  actions?: ReactNode;
}

const MainLayout = ({ children, title, actions }: MainLayoutProps) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isMobile = useIsMobile();
  const { isChatPanelOpen, setIsChatPanelOpen } = useChatPanel();
  const [isMobileFullscreen, setIsMobileFullscreen] = useState(false);

  // Effect to handle responsive behavior
  useEffect(() => {
    // Close chat panel on mobile by default
    if (isMobile) {
      setIsChatPanelOpen(false);
    } else {
      // Open chat panel on desktop by default
      setIsChatPanelOpen(true);
    }
  }, [isMobile, setIsChatPanelOpen]);

  // Handle chat panel close - memoized để tránh re-render
  const handleChatPanelClose = useCallback(() => {
    console.log('[MainLayout] Closing chat panel and clearing notifications');
    // Xóa tất cả thông báo khi đóng chat panel
    dispatch(clearChatNotifications());
    setIsChatPanelOpen(false);
    setIsMobileFullscreen(false);
  }, [dispatch, setIsChatPanelOpen]);

  // Handle chat panel open - memoized để tránh re-render
  const handleChatPanelOpen = useCallback(() => {
    setIsChatPanelOpen(true);
    if (isMobile) {
      setIsMobileFullscreen(true);
    }
  }, [isMobile, setIsChatPanelOpen]);

  // Handle keyword detection or path navigation - memoized để tránh re-render
  const handleKeywordDetected = useCallback(
    (keywordOrPath: string) => {
      console.log('Keyword or path detected:', keywordOrPath);

      // Nếu là đường dẫn (bắt đầu bằng '/')
      if (keywordOrPath.startsWith('/')) {
        navigate(keywordOrPath);
        return;
      }

      // Danh sách các từ khóa và đường dẫn tương ứng
      const keywordMappings: Record<string, string> = {
        // Home
        home: '/',
        'trang chủ': '/',
        'trang chu': '/',
        main: '/',
        dashboard: '/',
        'trang chinh': '/',

        // AI Agents
        'ai agents': '/ai-agents',
        'ai agent': '/ai-agents',
        aiagent: '/ai-agents',
        aiagents: '/ai-agents',
        agent: '/ai-agents',
        agents: '/ai-agents',
        bot: '/ai-agents',
        bots: '/ai-agents',
        'trợ lý': '/ai-agents',
        'tro ly': '/ai-agents',

        // Workflows
        workflow: '/workflows',
        workflows: '/workflows',
        'quy trình': '/workflows',
        'quy trinh': '/workflows',
        luồng: '/workflows',
        luong: '/workflows',

        // Integrations
        integration: '/integrations',
        integrations: '/integrations',
        'tích hợp': '/integrations',
        'tich hop': '/integrations',
        'kết nối': '/integrations',
        'ket noi': '/integrations',

        // Payment
        payment: '/payment',
        'thanh toán': '/payment',
        'thanh toan': '/payment',
        'nạp tiền': '/payment',
        'nap tien': '/payment',
        credit: '/payment',
        billing: '/payment',

        // R-Point Packages
        rpoint: '/rpoint-packages',
        'r-point': '/rpoint-packages',
        point: '/rpoint-packages',
        points: '/rpoint-packages',
        'gói point': '/rpoint-packages',
        'goi point': '/rpoint-packages',
        điểm: '/rpoint-packages',
        diem: '/rpoint-packages',
        'nạp điểm': '/rpoint-packages',
        'nap diem': '/rpoint-packages',

        // Subscription Packages
        subscription: '/subscription/packages',
        'gói dịch vụ': '/subscription/packages',
        'goi dich vu': '/subscription/packages',
        'dịch vụ': '/subscription/packages',
        'dich vu': '/subscription/packages',
        'đăng ký': '/subscription/packages',
        'dang ky': '/subscription/packages',
        service: '/subscription/packages',
        services: '/subscription/packages',
        packages: '/subscription/packages',

        // Settings
        settings: '/settings',
        'cài đặt': '/settings',
        'cai dat': '/settings',
        'thiết lập': '/settings',
        'thiet lap': '/settings',
        config: '/settings',

        // Profile
        profile: '/profile',
        'hồ sơ': '/profile',
        'ho so': '/profile',
        'tài khoản': '/profile',
        'tai khoan': '/profile',
        account: '/profile',
        user: '/profile',

        // Help
        help: '/help',
        'trợ giúp': '/help',
        'tro giup': '/help',
        'hỗ trợ': '/help',
        'ho tro': '/help',
        support: '/help',
        'hướng dẫn': '/help',
        'huong dan': '/help',

        // Animation Demo
        animation: '/animation-demo',
        'hiệu ứng': '/animation-demo',
        'hieu ung': '/animation-demo',

        // Responsive Demo
        responsive: '/responsive-demo',

        // Components Library
        component: '/components',
        components: '/components',
        'thành phần': '/components',
        'thanh phan': '/components',
        'component library': '/components',
        'components library': '/components',
        'thư viện component': '/components',
        'thu vien component': '/components',
        'ui components': '/components',
        'ui library': '/components',

        // Component Categories
        button: '/components/buttons',
        buttons: '/components/buttons',
        'nút bấm': '/components/buttons',
        'nut bam': '/components/buttons',

        card: '/components/cards',
        cards: '/components/cards',
        thẻ: '/components/cards',
        the: '/components/cards',

        input: '/components/inputs',
        inputs: '/components/inputs',
        'ô nhập': '/components/inputs',
        'o nhap': '/components/inputs',

        layout: '/components/layout',
        'bố cục': '/components/layout',
        'bo cuc': '/components/layout',

        theme: '/components/theme',
        'giao diện': '/components/theme',
        'giao dien': '/components/theme',

        // Form Demo
        form: '/form-demo',
        'biểu mẫu': '/form-demo',
        'bieu mau': '/form-demo',

        // Form Conditional Demo
        'form conditional': '/form-conditional-demo',
        'conditional form': '/form-conditional-demo',
        'form có điều kiện': '/form-conditional-demo',
        'form co dieu kien': '/form-conditional-demo',

        // Form Layout Demo
        'form layout': '/form-layout-demo',
        'layout form': '/form-layout-demo',
        'form grid': '/form-layout-demo',
        'grid form': '/form-layout-demo',
        'form inline': '/form-layout-demo',
        'inline form': '/form-layout-demo',
        'form horizontal': '/form-layout-demo',
        'horizontal form': '/form-layout-demo',

        // Form Field Dependencies Demo
        'form dependency': '/form-dependency-demo',
        'form dependencies': '/form-dependency-demo',
        'field dependency': '/form-dependency-demo',
        'field dependencies': '/form-dependency-demo',
        'cascading select': '/form-dependency-demo',
        'dependent fields': '/form-dependency-demo',

        // Form Sections Demo
        'form section': '/components/form-sections',
        'form sections': '/components/form-sections',
        'section form': '/components/form-sections',
        'sections form': '/components/form-sections',
        'collapsible form': '/components/form-sections',
        'collapsible sections': '/components/form-sections',
      };

      // Chuyển từ khóa thành chữ thường để so sánh
      const keyword = keywordOrPath.toLowerCase().trim();

      // Kiểm tra từ khóa chính xác
      if (keywordMappings[keyword]) {
        navigate(keywordMappings[keyword]);
        return;
      }

      // Kiểm tra từ khóa một phần
      for (const [key, path] of Object.entries(keywordMappings)) {
        if (keyword.includes(key) || key.includes(keyword)) {
          navigate(path);
          console.log(
            `Partial keyword match: "${keyword}" contains or is contained in "${key}" -> navigating to ${path}`
          );
          return;
        }
      }

      console.log(`No matching keyword found for: "${keyword}"`);
    },
    [navigate]
  );

  // Sử dụng menu items từ file menu-items.ts
  const menuItems = userMenuItems;

  // Sử dụng StableViewPanel để tối ưu hóa
  const viewPanelElement = (
    <StableViewPanel title={title} actions={actions}>
      {children}
    </StableViewPanel>
  );

  // WebSocket configuration cho ChatPanel
  const websocketConfig = useMemo(() => ({
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: localStorage.getItem('access_token') || undefined,
    },
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    timeout: 20000,
  }), []);

  // Memoize ChatPanel với WebSocket mode
  const memoizedChatPanel = useMemo(
    () => (
      <ChatPanel
        onClose={handleChatPanelClose}
        onKeywordDetected={handleKeywordDetected}
        mode="websocket"
        websocketConfig={websocketConfig}
      />
    ),
    [handleChatPanelClose, handleKeywordDetected, websocketConfig]
  );

  // Memoize SidebarMenu items để tránh re-render không cần thiết
  const memoizedSidebarItems = useMemo(
    () =>
      menuItems.map(item => ({
        icon: <Icon name={item.icon} size="lg" />, // Dynamically set icon
        tooltip: item.label, // Tooltip from label
        path: item.path, // Path from menuItems
      })),
    [menuItems]
  );

  return (
    <div className="h-screen w-full max-w-full overflow-hidden">
      {/* Mobile fullscreen chat panel - overlay */}
      {isMobile && isMobileFullscreen && (
        <div className="fixed inset-0 z-50 bg-white dark:bg-dark w-full">{memoizedChatPanel}</div>
      )}

      {/* Main layout container */}
      <div className="flex h-full w-full max-w-full overflow-hidden">
        {/* Sidebar menu - when chat panel is closed */}
        {!isChatPanelOpen && !isMobile && (
          <div className="h-full">
            <SidebarMenu onOpenChat={handleChatPanelOpen} items={memoizedSidebarItems} />
          </div>
        )}

        {/* Resizable Chat Layout - handles both chat panel and view panel */}
        <div className="flex-1 w-full max-w-full overflow-hidden">
          <ResizableChatLayout
            chatPanel={memoizedChatPanel}
            viewPanel={viewPanelElement}
            isChatPanelOpen={isChatPanelOpen}
            isMobile={isMobile}
          />
        </div>

        {/* Chat button - only visible on mobile when chat panel is closed */}
        {isMobile && !isChatPanelOpen && (
          <ChatButton
            onClick={handleChatPanelOpen}
            position="bottom-right"
            offset={20}
            size={36}
            hoverEffect="scale"
            className="animate-fade-in"
          />
        )}
      </div>
    </div>
  );
};

export default MainLayout;
