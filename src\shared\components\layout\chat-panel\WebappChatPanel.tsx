/**
 * Webapp Chat Panel với WebSocket
 * <PERSON>yên dụng cho chat với AI agent
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useWebappChatWebSocket } from '@/shared/hooks/chat/useWebappChatWebSocket';
import { WebappChatConfig } from '@/shared/services/webapp-chat-websocket.service';
import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { Typography } from '@/shared/components/common';

import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';
import ChatContent from './ChatContent';

// Props interface
interface WebappChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  conversationId?: number;
  websocketConfig?: WebappChatConfig;
  className?: string;
}

// Message type cho ChatContent
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
}

/**
 * Webapp Chat Panel Component
 */
const WebappChatPanel: React.FC<WebappChatPanelProps> = ({
  onClose,
  onKeywordDetected,
  conversationId,
  websocketConfig = {
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: localStorage.getItem('access_token') || undefined,
    },
  },
  className = '',
}) => {
  const { t } = useTranslation(['common', 'chat']);
  const { notifications, addNotification, removeNotification } = useChatNotification();

  // WebSocket chat hook
  const {
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    joinConversation,
    currentConversationId,
    messages: wsMessages,
    sendMessage,
    clearMessages,
    streamingMessage,
    isStreaming,
    isAITyping,
    startTyping,
    stopTyping,
    lastError,
    clearError,
  } = useWebappChatWebSocket({
    config: websocketConfig,
    conversationId,
    autoJoinConversation: true,
    enableTypingIndicator: true,
  });

  // Local state
  const [isLoading, setIsLoading] = useState(false);

  // Convert WebSocket messages to ChatContent format
  const messages: Message[] = React.useMemo(() => {
    const convertedMessages = wsMessages.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.sender,
      timestamp: new Date(msg.timestamp),
      avatar: msg.sender === 'ai' ? '/assets/images/ai-agents/assistant-robot.svg' : undefined,
    }));

    // Add streaming message if exists
    if (streamingMessage) {
      convertedMessages.push({
        id: streamingMessage.id,
        content: streamingMessage.content,
        sender: streamingMessage.sender,
        timestamp: new Date(streamingMessage.timestamp),
        avatar: '/assets/images/ai-agents/assistant-robot.svg',
      });
    }

    return convertedMessages;
  }, [wsMessages, streamingMessage]);

  // Show notification helper
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    addNotification({
      id: Date.now().toString(),
      type,
      message,
      duration: 5000,
    });
  };

  // Handle connection errors
  useEffect(() => {
    if (lastError) {
      showNotification('error', lastError);
      clearError();
    }
  }, [lastError, clearError]);

  // Handle connection status changes
  useEffect(() => {
    if (connectionStatus === 'connected') {
      showNotification('success', t('chat:connected'));
    } else if (connectionStatus === 'error') {
      showNotification('error', t('chat:connectionError'));
    }
  }, [connectionStatus, t]);

  // Handle new chat
  const handleNewChat = () => {
    clearMessages();
    clearError();
    
    // Join new conversation if needed
    if (currentConversationId) {
      joinConversation();
    }
  };

  // Handle send message
  const handleSendMessage = async (content: string) => {
    try {
      setIsLoading(true);
      await sendMessage(content);
    } catch (error) {
      console.error('Failed to send message:', error);
      showNotification('error', t('chat:sendMessageError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle typing events
  const handleInputChange = (value: string) => {
    if (value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  // Render connection status
  const renderConnectionStatus = () => {
    if (connectionStatus === 'connecting') {
      return (
        <div className="px-4 py-2 bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800">
          <Typography variant="body2" className="text-yellow-700 dark:text-yellow-300">
            {t('chat:connecting')}...
          </Typography>
        </div>
      );
    }

    if (connectionStatus === 'error' || !isConnected) {
      return (
        <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center justify-between">
            <Typography variant="body2" className="text-red-700 dark:text-red-300">
              {t('chat:connectionError')}
            </Typography>
            <button
              onClick={connect}
              className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
            >
              {t('chat:reconnect')}
            </button>
          </div>
        </div>
      );
    }

    return null;
  };

  // Render AI typing indicator
  const renderTypingIndicator = () => {
    if (isAITyping || isStreaming) {
      return (
        <div className="px-4 py-2">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <img 
                src="/assets/images/ai-agents/assistant-robot.svg" 
                alt="AI" 
                className="w-5 h-5"
              />
            </div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {isStreaming ? t('chat:aiResponding') : t('chat:aiTyping')}
            </Typography>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-dark relative w-full ${className}`}>
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm">
        <ChatHeader onNewChat={handleNewChat} onClose={onClose} />
        {renderConnectionStatus()}
      </div>

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto">
        <ChatContent
          messages={messages}
          isLoading={isLoading}
          notifications={notifications}
          onRemoveNotification={removeNotification}
        />
        {renderTypingIndicator()}
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ChatInput
          onSendMessage={handleSendMessage}
          onKeywordDetected={onKeywordDetected}
          onInputChange={handleInputChange}
          showNotification={showNotification}
          disabled={!isConnected || isLoading}
          placeholder={
            !isConnected 
              ? t('chat:notConnected')
              : isLoading 
                ? t('chat:sending')
                : t('chat:typeMessage')
          }
        />
      </div>

      {/* Debug info (chỉ hiển thị trong development) */}
      {import.meta.env.DEV && (
        <div className="absolute bottom-0 right-0 p-2 bg-black bg-opacity-50 text-white text-xs">
          <div>Status: {connectionStatus}</div>
          <div>Conversation: {currentConversationId}</div>
          <div>Messages: {messages.length}</div>
          {isStreaming && <div>Streaming: Yes</div>}
        </div>
      )}
    </div>
  );
};

export default WebappChatPanel;
