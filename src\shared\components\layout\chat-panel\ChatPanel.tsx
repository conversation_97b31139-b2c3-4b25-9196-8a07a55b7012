import React from 'react';
import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useWebappChatWebSocket } from '@/shared/hooks/chat/useWebappChatWebSocket';
import { WebappChatConfig } from '@/shared/services/webapp-chat-websocket.service';
import { Typography } from '@/shared/components/common';
import ChatContent from './ChatContent';
import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';

// Message type
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
}

interface ChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  // WebSocket configuration (optional)
  websocketConfig?: WebappChatConfig;
  conversationId?: number;
  // Mode: 'websocket' for real chat, 'demo' for simulation
  mode?: 'websocket' | 'demo';
}

const ChatPanel = ({
  onClose,
  onKeywordDetected,
  websocketConfig,
  conversationId,
  mode = 'websocket' // Default to WebSocket mode
}: ChatPanelProps) => {
  const { t } = useTranslation(['common', 'chat']);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { notifications, addNotification, removeNotification } = useChatNotification();

  // Default WebSocket config
  const defaultWebSocketConfig: WebappChatConfig = {
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: localStorage.getItem('access_token') || undefined,
    },
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    timeout: 20000,
  };

  // Use WebSocket only if mode is 'websocket'
  const shouldUseWebSocket = mode === 'websocket';

  // WebSocket chat hook (only when enabled)
  const {
    isConnected,
    connectionStatus,
    connect,
    currentConversationId,
    messages: wsMessages,
    sendMessage: wsSendMessage,
    clearMessages: wsClearMessages,
    streamingMessage,
    isStreaming,
    isAITyping,
    startTyping,
    stopTyping,
    lastError,
    clearError,
  } = useWebappChatWebSocket(shouldUseWebSocket ? {
    config: websocketConfig || defaultWebSocketConfig,
    conversationId,
    autoJoinConversation: true,
    enableTypingIndicator: true,
  } : {});

  // Convert WebSocket messages to ChatContent format
  const allMessages: Message[] = React.useMemo(() => {
    if (!shouldUseWebSocket) {
      return messages; // Use local state for demo mode
    }

    const convertedMessages = wsMessages.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.sender,
      timestamp: new Date(msg.timestamp),
      avatar: msg.sender === 'ai' ? '/assets/images/ai-agents/assistant-robot.svg' : undefined,
    }));

    // Add streaming message if exists
    if (streamingMessage) {
      convertedMessages.push({
        id: streamingMessage.id,
        content: streamingMessage.content,
        sender: streamingMessage.sender,
        timestamp: new Date(streamingMessage.timestamp),
        avatar: '/assets/images/ai-agents/assistant-robot.svg',
      });
    }

    return convertedMessages;
  }, [shouldUseWebSocket, messages, wsMessages, streamingMessage]);

  // Handle connection errors
  useEffect(() => {
    if (shouldUseWebSocket && lastError) {
      showNotification('error', lastError);
      clearError();
    }
  }, [shouldUseWebSocket, lastError, clearError]);

  // Handle connection status changes
  useEffect(() => {
    if (!shouldUseWebSocket) return;

    if (connectionStatus === 'connected') {
      showNotification('success', t('chat:connected'));
    } else if (connectionStatus === 'error') {
      showNotification('error', t('chat:connectionError'));
    }
  }, [shouldUseWebSocket, connectionStatus, t]);

  // Handle new chat
  const handleNewChat = () => {
    if (shouldUseWebSocket) {
      wsClearMessages();
      clearError();
    } else {
      setMessages([]);
    }
  };

  // Handle send message
  const handleSendMessage = async (content: string) => {
    if (shouldUseWebSocket) {
      // WebSocket mode - real chat
      try {
        setIsLoading(true);
        await wsSendMessage(content);
      } catch (error) {
        console.error('Failed to send message:', error);
        showNotification('error', t('chat:sendMessageError'));
      } finally {
        setIsLoading(false);
      }
    } else {
      // Demo mode - simulate chat
      const userMessage: Message = {
        id: Date.now().toString(),
        content,
        sender: 'user',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setIsLoading(true);

      // Simulate AI response after a delay
      setTimeout(() => {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `This is a response to: "${content}"`,
          sender: 'ai',
          timestamp: new Date(),
          avatar: '/assets/images/ai-agents/assistant-robot.svg',
        };

        setMessages(prev => [...prev, aiMessage]);
        setIsLoading(false);
      }, 1000);
    }
  };

  // Handle typing events
  const handleInputChange = (value: string) => {
    if (shouldUseWebSocket) {
      if (value.trim()) {
        startTyping();
      } else {
        stopTyping();
      }
    }
  };

  // Custom notification handler
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    console.log(`[ChatPanel] Showing notification: ${type} - ${message}`);

    // Thêm notification mới và tự động xóa sau 5 giây
    const id = addNotification(type, message, 5000);
    console.log(`[ChatPanel] Added notification with ID: ${id}`);

    // Kiểm tra xem notification đã được thêm vào state chưa
    console.log(`[ChatPanel] Current notifications:`, notifications);

    // Đảm bảo ChatContent được cập nhật
    setTimeout(() => {
      console.log(
        `[ChatPanel] Checking if notification ${id} is visible:`,
        notifications.some((n: unknown) => (n as unknown as { id: string }).id === id)
      );
    }, 500);

    return id;
  };

  // Render connection status (only for WebSocket mode)
  const renderConnectionStatus = () => {
    if (!shouldUseWebSocket) return null;

    if (connectionStatus === 'connecting') {
      return (
        <div className="px-4 py-2 bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800">
          <Typography variant="body2" className="text-yellow-700 dark:text-yellow-300">
            {t('chat:connecting')}...
          </Typography>
        </div>
      );
    }

    if (connectionStatus === 'error' || !isConnected) {
      return (
        <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center justify-between">
            <Typography variant="body2" className="text-red-700 dark:text-red-300">
              {t('chat:connectionError')}
            </Typography>
            <button
              onClick={connect}
              className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
            >
              {t('chat:reconnect')}
            </button>
          </div>
        </div>
      );
    }

    return null;
  };

  // Render AI typing indicator (only for WebSocket mode)
  const renderTypingIndicator = () => {
    if (!shouldUseWebSocket || (!isAITyping && !isStreaming)) return null;

    return (
      <div className="px-4 py-2">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <img
              src="/assets/images/ai-agents/assistant-robot.svg"
              alt="AI"
              className="w-5 h-5"
            />
          </div>
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
            {isStreaming ? t('chat:aiResponding') : t('chat:aiTyping')}
          </Typography>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-dark relative w-full">
      {/* Header cố định ở trên cùng với z-index cao */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm flex-shrink-0">
        <ChatHeader onNewChat={handleNewChat} onClose={onClose} />
        {renderConnectionStatus()}
      </div>

      {/* Messages area - scrollable */}
      <div className="flex-1 overflow-y-auto">
        <ChatContent
          messages={allMessages}
          isLoading={isLoading || (shouldUseWebSocket && (isAITyping || isStreaming))}
          notifications={notifications}
          onRemoveNotification={removeNotification}
        />
        {renderTypingIndicator()}
      </div>

      {/* Input area - fixed at bottom */}
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ChatInput
          onSendMessage={handleSendMessage}
          onKeywordDetected={onKeywordDetected}
          onInputChange={handleInputChange}
          showNotification={showNotification}
          disabled={shouldUseWebSocket && (!isConnected || isLoading)}
          placeholder={
            shouldUseWebSocket
              ? (!isConnected
                  ? t('chat:notConnected')
                  : isLoading
                    ? t('chat:sending')
                    : t('chat:typeMessage'))
              : t('chat:typeMessage')
          }
        />
      </div>

      {/* Debug info (chỉ hiển thị trong development và WebSocket mode) */}
      {import.meta.env.DEV && shouldUseWebSocket && (
        <div className="absolute bottom-0 right-0 p-2 bg-black bg-opacity-50 text-white text-xs">
          <div>Mode: {mode}</div>
          <div>Status: {connectionStatus}</div>
          <div>Conversation: {currentConversationId}</div>
          <div>Messages: {allMessages.length}</div>
          {isStreaming && <div>Streaming: Yes</div>}
        </div>
      )}
    </div>
  );
};

export default ChatPanel;
