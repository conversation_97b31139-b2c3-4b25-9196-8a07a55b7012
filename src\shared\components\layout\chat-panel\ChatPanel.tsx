import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { WebappChatConfig } from '@/shared/services/webapp-chat-websocket.service';
import { useState } from 'react';
import ChatContent from './ChatContent';
import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';

// Message type
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
}

interface ChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  // WebSocket configuration (optional)
  websocketConfig?: WebappChatConfig;
  conversationId?: number;
  // Mode: 'websocket' for real chat, 'demo' for simulation
  mode?: 'websocket' | 'demo';
}

const ChatPanel = ({ onClose, onKeywordDetected }: ChatPanelProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { notifications, addNotification, removeNotification } = useChatNotification();

  // Handle new chat
  const handleNewChat = () => {
    setMessages([]);
  };

  // Handle send message
  const handleSendMessage = (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Simulate AI response after a delay
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `This is a response to: "${content}"`,
        sender: 'ai',
        timestamp: new Date(),
        avatar: '/assets/images/ai-agents/assistant-robot.svg',
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000);
  };

  // Custom notification handler
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    console.log(`[ChatPanel] Showing notification: ${type} - ${message}`);

    // Thêm notification mới và tự động xóa sau 5 giây
    const id = addNotification(type, message, 5000);
    console.log(`[ChatPanel] Added notification with ID: ${id}`);

    // Kiểm tra xem notification đã được thêm vào state chưa
    console.log(`[ChatPanel] Current notifications:`, notifications);

    // Đảm bảo ChatContent được cập nhật
    setTimeout(() => {
      console.log(
        `[ChatPanel] Checking if notification ${id} is visible:`,
        notifications.some((n: unknown) => (n as unknown as { id: string }).id === id)
      );
    }, 500);

    return id;
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-dark relative w-full">
      {/* Header cố định ở trên cùng với z-index cao */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm mb-4 flex-shrink-0">
        <ChatHeader onNewChat={handleNewChat} onClose={onClose} />
      </div>

      {/* Uncomment to test notifications */}
      {/* <TestNotification /> */}

      {/* Messages area - scrollable */}
      <div className="flex-1 overflow-hidden">
        <ChatContent
          messages={messages}
          isLoading={isLoading}
          notifications={notifications}
          onRemoveNotification={removeNotification}
        />
      </div>

      {/* Input area - fixed at bottom */}
      <div className="flex-shrink-0">
        <ChatInput
          onSendMessage={handleSendMessage}
          onKeywordDetected={onKeywordDetected}
          showNotification={showNotification}
        />
      </div>
    </div>
  );
};

export default ChatPanel;
